import { useParams } from '@tanstack/react-router'

import { InvestmentPlanUpdateTableMap } from './constants'

export function TableUpdate() {
  const params = useParams({ strict: false })
  const tableName =
    params.tableName as keyof typeof InvestmentPlanUpdateTableMap
  const TableComponent =
    tableName && tableName in InvestmentPlanUpdateTableMap
      ? InvestmentPlanUpdateTableMap[tableName]
      : null

  if (!TableComponent) {
    return null
  }

  return <TableComponent />
}
