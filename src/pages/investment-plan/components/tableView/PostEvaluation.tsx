import { useQuery } from '@tanstack/react-query'
import { usePara<PERSON>, useSearch, useRouter } from '@tanstack/react-router'
import { Card, Button, Table, Typography, type TableColumnsType } from 'antd'
import { createStyles } from 'antd-style'
import numeral from 'numeral'
import { useMemo, useEffect, useState } from 'react'

import { type APIResponse, request } from '@/lib/request.ts'
import { getIndustryNewTypePath } from '@/universal/basic-form'
import {
  INDUSTRY_TYPE,
  PROJECT_CATEGORY,
} from '@/universal/basic-form/constants.ts'

import { InvestmentPlanTableNamesMap } from '../../constants.ts'

import type { PostEvaluation } from './types.ts'

const useStyle = createStyles(({ css }) => {
  const antCls = '.ant'
  return {
    customTable: css`
      ${antCls}-table-thead > tr > th {
        background-color: #e5ebfe;
        font-weight: 400;
      }
    `,
  }
})

export const PostEvaluationView = ({
  page = 'PLAN',
}: {
  page?: 'PLAN' | 'COMPLETION'
}) => {
  const { styles } = useStyle()
  const { tableName } = useParams({ strict: false })
  const summaryData = useSearch({
    strict: false,
  }) as {
    id: string
    year: string
    company_id: string
    company_name: string
    consolidation: string
  }

  const [filters, setFilters] = useState({
    page_num: 0,
    page_size: 0,
    use_total: 1,
    belong_company_id: '',
    year: '',
    consolidation: '',
  })

  useEffect(() => {
    if (summaryData?.year) {
      setFilters((prev) => ({
        ...prev,
        year: summaryData.year,
        belong_company_id: summaryData.company_id,
        consolidation: summaryData.consolidation,
      }))
    }
  }, [
    setFilters,
    summaryData?.year,
    summaryData?.company_id,
    summaryData?.consolidation,
  ])

  // 获取初始数据
  const { data, isLoading } = useQuery({
    queryKey: ['/post-evaluation/list', filters] as const,
    queryFn: async ({ queryKey: [url, filters] }) => {
      const response = await request<
        APIResponse<{
          Total: number
          Data: PostEvaluation[]
        }>
      >(url as string, {
        query: {
          ...filters,
        },
      })
      if (response.code !== 200001) return null
      return response?.data ?? null
    },
    staleTime: 0,
    retry: false,
    enabled: !!filters?.year && !!filters?.belong_company_id,
  })

  // 表格列配置
  const columns: TableColumnsType<PostEvaluation> = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '项目名称',
        dataIndex: 'project_name',
        width: 160,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {value}
          </Typography.Text>
        ),
      },
      {
        title: '固资/股权',
        dataIndex: 'project_style',
        width: 120,
        render: (value) => {
          return value === 1 ? '固定资产投资' : value === 2 ? '股权投资' : '-'
        },
      },
      {
        title: '境内/境外',
        dataIndex: 'region',
        width: 100,
        render: (value) => {
          return value === 1 ? '境内' : '境外'
        },
      },
      {
        title: (
          <Typography.Text
            ellipsis={{ tooltip: '省、自治区、直辖市/国家（地区）' }}
          >
            省、自治区、直辖市/国家（地区）
          </Typography.Text>
        ),
        dataIndex: 'project_area',
        width: 150,
      },
      {
        title: '主业/非主业',
        dataIndex: 'is_major',
        width: 100,
        render: (value) => {
          return value === 1 ? '主业' : '非主业'
        },
      },
      {
        title: '项目分类',
        dataIndex: 'project_category',
        render: (value) => {
          return value
            ?.split(',')
            .map(
              (item: string) =>
                PROJECT_CATEGORY.find((i) => i.value === item)?.label + ',',
            )
        },
      },
      {
        title: '所属行业',
        dataIndex: 'industry_type',
        width: 100,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => {
          return (
            INDUSTRY_TYPE.find((item) => item.value === value)?.label + value
          )
        },
      },
      {
        title: '所属战新产业',
        dataIndex: 'industry_new_type',
        width: 200,
        ellipsis: {
          showTitle: false,
        },
        render: (value) => {
          const industryNewType = getIndustryNewTypePath(value).join('/')
          return (
            <Typography.Text ellipsis={{ tooltip: industryNewType }}>
              {industryNewType}
            </Typography.Text>
          )
        },
      },
      {
        title: '项目总投资（万元）',
        dataIndex: 'project_total_investment',
        align: 'right',
        render: (value) => (
          <Typography.Text ellipsis={{ tooltip: value }}>
            {numeral(value).format('0,0.00')}
          </Typography.Text>
        ),
        width: 200,
      },
      {
        title: '项目开始时间',
        dataIndex: 'start_time',
        width: 180,
      },
      {
        title: '项目完成或预计完成时间',
        dataIndex: 'complete_time_expect',
        width: 180,
      },
      {
        title: '组织形式',
        dataIndex: 'organization_type',
        width: 100,
        render: (value) => {
          return value === 1 ? '集团组织' : '子企业组织'
        },
      },
      {
        title: '评价方式',
        dataIndex: 'eval_type',
        width: 100,
        render: (value) => {
          return value === 1 ? '企业自评' : '第三方评价'
        },
      },
      ...(page === 'PLAN'
        ? [
            {
              title: '备注',
              dataIndex: 'remarks',
              width: 200,
              ellipsis: {
                showTitle: false,
              },
              render: (value: string) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                  {value}
                </Typography.Text>
              ),
            },
          ]
        : [
            {
              title: '工作成效',
              dataIndex: 'performance',
              width: 200,
              ellipsis: {
                showTitle: false,
              },
              render: (value: string) => (
                <Typography.Text ellipsis={{ tooltip: value }}>
                  {value}
                </Typography.Text>
              ),
            },
          ]),
    ]
  }, [page])

  const title =
    InvestmentPlanTableNamesMap[
      tableName as keyof typeof InvestmentPlanTableNamesMap
    ]
  const router = useRouter()

  return (
    <div className="flex h-full flex-col">
      <Card title={title}>
        <div className="mb-4 flex items-center justify-between">
          <p>填报年份：{summaryData?.year}</p>
          <p>编制单位：{summaryData?.company_name}</p>
          <p>金额：万元</p>
        </div>
        <Table
          size="small"
          tableLayout="auto"
          bordered
          className={styles.customTable}
          dataSource={data?.Data ?? []}
          loading={isLoading}
          columns={columns}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: 48 }}
          pagination={false}
          rowKey="approval_id"
        />
        <div className="sticky bottom-0 flex justify-end gap-2 bg-white py-2">
          <Button onClick={() => router.history.back()}>返回</Button>
        </div>
      </Card>
    </div>
  )
}
