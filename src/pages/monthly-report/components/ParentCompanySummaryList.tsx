import { useQuery } from '@tanstack/react-query'
import { useNavigate } from '@tanstack/react-router'
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  message,
  Row,
  Table,
  type TableColumnsType,
} from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

import { FormItemPrefix } from '@/components/FormItemPrefix'
import { useAuth } from '@/contexts/auth'
import { useCompany } from '@/contexts/company'
import { request, type APIResponse } from '@/lib/request'
import { checkUndefinedProperties, flattenTreeToArray } from '@/universal'
import { COMPANY_LEVEL } from '@/universal/data-summary/constants'
import type { MonthlyReportDTO } from '@/universal/data-summary/types'

export const ParentCompanySummaryList = () => {
  const { user, company } = useAuth()
  const { companyTree } = useCompany()

  const navigate = useNavigate()

  const [pagination, setPagination] = useState({ page_num: 1, page_size: 10 })
  const [queryParams, setQueryParams] = useState({})

  const getTableData = useQuery({
    queryKey: [user?.company_id, queryParams, pagination],
    queryFn: async ({ queryKey: [company_id, queryParams, pagination] }) => {
      const res = await request<
        APIResponse<{ Data: MonthlyReportDTO[]; Total: number }>
      >('/aggregate/monthly-report/list-aggregate', {
        query: {
          company_id,
          consolidation: 1,
          use_total: 1,
          ...queryParams,
          ...pagination,
        },
      })
      if (res.code === 200001) {
        return res?.data
      }
      message.error(res.message)
      return { Data: [], Total: 0 }
    },
    enabled: !!user?.company_id,
    refetchOnWindowFocus: false,
  })

  const columns: TableColumnsType<MonthlyReportDTO> = [
    {
      title: '填报周期',
      dataIndex: 'period',
      render: (value) => dayjs(value).format('YYYY-MM'),
    },
    {
      title: '汇总单位',
      dataIndex: 'company_name',
      render: (value, record) => {
        const companyLevel = flattenTreeToArray(companyTree)?.filter(
          (item) => item.id === record.company_id,
        )[0]?.level

        return companyLevel === company?.level ? (
          <span>{value + '（本级）'}</span>
        ) : (
          <span>{value}</span>
        )
      },
    },
    {
      title: '已提交公司数量',
      dataIndex: 'report_count',
    },
    {
      title: '本月合计 (万元)',
      dataIndex: 'total_amount',
      align: 'right',
      sorter: (a, b) => a.total_amount - b.total_amount,
    },
    {
      title: '上报人',
      dataIndex: 'op_name',
    },
    {
      title: '上报日期',
      dataIndex: 'approval_update_at',
      render: (value) =>
        dayjs(value).format('YYYY-MM-DD HH:mm:ss') === '1901-01-01 08:00:00'
          ? ''
          : dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      fixed: 'right',
      dataIndex: 'action',
      render: (_, record) => (
        <>
          {record.approval_node_status === 3 ? (
            <Button
              size="small"
              type="link"
              onClick={() =>
                navigate({
                  to: '/data-summary/monthly-report/$id/view',
                  params: { id: record.approval_node_id },
                  search: { reportPeriod: record.period },
                })
              }
            >
              查看汇总历史
            </Button>
          ) : (
            <Button
              size="small"
              type="link"
              onClick={() =>
                navigate({
                  to: '/data-summary/monthly-report/$id/report',
                  params: { id: record.approval_node_id },
                  search: { reportPeriod: record.period, itemId: record.id },
                })
              }
            >
              {company?.level === COMPANY_LEVEL.GROUP
                ? '汇总上报国资委'
                : '汇总上报集团'}
            </Button>
          )}
          <Divider type="vertical" />
          <Button size="small" type="link">
            导出数据
          </Button>
        </>
      ),
    },
  ]

  return (
    <div className="mt-4">
      <Card>
        <Form
          onFinish={(value) => {
            if (checkUndefinedProperties(value)) getTableData.refetch()
            const { period, date } = value
            const [start_time, end_time] = date || []
            setQueryParams({
              period:
                period &&
                dayjs(period).startOf('month').format('YYYY-MM-DD HH:mm:ss'),
              start_time:
                start_time && dayjs(start_time).format('YYYY-MM-DD HH:mm:ss'),
              end_time:
                end_time && dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
            })
          }}
          onReset={() => {
            setQueryParams({})
            setPagination({ page_num: 1, page_size: 10 })
          }}
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="period">
                <DatePicker
                  picker="month"
                  prefix={<FormItemPrefix title="填报周期" />}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="date">
                <DatePicker.RangePicker
                  prefix={<FormItemPrefix title="创建时间" />}
                  className="w-full"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <div className="flex w-full justify-end gap-2">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={getTableData.isFetching}
                >
                  搜索
                </Button>
                <Button htmlType="reset" loading={getTableData.isFetching}>
                  清空
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
        <div className="font-semibold">投资月报</div>
        <Divider />
        <Table
          loading={getTableData.isFetching}
          columns={columns}
          size="small"
          dataSource={getTableData.data?.Data}
          pagination={{
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal(total, range) {
              return `共 ${total} 条数据, 显示第 ${range[0]} - ${range[1]} 条`
            },
            onChange: (page, pageSize) => {
              setPagination({ page_num: page, page_size: pageSize })
            },
          }}
          rowKey="id"
          scroll={{ x: 'max-content' }}
        />
      </Card>
    </div>
  )
}
