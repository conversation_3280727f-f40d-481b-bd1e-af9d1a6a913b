import { useQuery } from '@tanstack/react-query'
import { <PERSON>, useRouter, useSearch } from '@tanstack/react-router'
import { Button, Card, Table, type TableColumnsType } from 'antd'
import { CalendarClockIcon } from 'lucide-react'
import numeral from 'numeral'
import { useMemo } from 'react'

import SkeletonTable, {
  type SkeletonTableColumnsType,
} from '@/components/SkeletonTable'
import { type APIResponse, request } from '@/lib/request'

import { CompletionStatusTableNamesMap } from './constants'
import type { CompletionTotalPageDTO } from './types'

export function CompletionStatusList() {
  const router = useRouter()
  const { id, year, quarter, company_id, company_name } = useSearch({
    strict: false,
  }) as {
    id: string
    year: string
    quarter: string
    company_id: string
    company_name: string
  }

  const { data: statisticalData } = useQuery({
    queryKey: ['/aggregate/completed/summary', company_id, year] as const,
    queryFn: async ({ queryKey: [url, company_id, year] }) => {
      const response = await request<APIResponse<CompletionTotalPageDTO>>(
        url as string,
        {
          query: {
            company_id,
            consolidation: 2, //所有
            year: year,
          },
        },
      )
      if (response.code !== 200001) return null
      return response?.data
    },
    staleTime: 0,
    retry: false,
    enabled: !!company_id,
  })

  const tableData = useMemo(() => {
    return Object.entries(CompletionStatusTableNamesMap).map(
      ([key, value]) => ({
        label: value,
        value: key,
      }),
    )
  }, [])

  const columns = useMemo(() => {
    return [
      {
        title: '序号',
        align: 'center',
        width: 60,
        render: (_, __, index) => {
          return index + 1
        },
      },
      {
        title: '表单名称',
        dataIndex: 'label',
        minWidth: 160,
        render: (label, record) => (
          <Link
            to="/data-summary/completion-status/table/$tableName"
            search={{
              id,
              year,
              quarter,
              company_id,
              company_name,
            }}
            params={{ tableName: record.value }}
          >
            {label}
          </Link>
        ),
      },
    ] as TableColumnsType<(typeof tableData)[number]>
  }, [id, year, quarter, company_id, company_name])

  return (
    <div className="flex h-full flex-col gap-4">
      <Button
        className="-my-4 self-start"
        type="link"
        onClick={() => router.history.back()}
      >
        {'<< 返回上一页'}
      </Button>
      <Card>
        <div className="space-y-6">
          <div className="flex justify-between">
            <h2 className="text-xl font-semibold">{company_name}</h2>
          </div>
          <div className="flex items-center gap-2">
            <CalendarClockIcon className="size-4" />
            <span className="text-sm text-[#666]">本年度完成投资总额：</span>
            <span className="text-xl font-semibold">
              {numeral(statisticalData?.current_year_total).format('0,0.00')}
              万元
            </span>
          </div>
        </div>
      </Card>
      <Card title={`投资完成情况-${year}_Q${quarter}`}>
        <SkeletonTable columns={columns as SkeletonTableColumnsType[]}>
          <Table
            rowKey="value"
            size="small"
            pagination={false}
            dataSource={tableData}
            columns={columns}
            scroll={{ x: 'max-content' }}
            sticky={{ offsetHeader: 48 }}
          />
        </SkeletonTable>
      </Card>
    </div>
  )
}
